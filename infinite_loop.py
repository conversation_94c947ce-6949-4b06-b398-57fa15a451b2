#!/usr/bin/env python3
"""
Infinite Loop Script - Auto-Execution Every 15 Minutes

This script runs 'python .\\start_all_python.py' every 15 minutes automatically.
Just run this script once with: python infinite_loop.py
It will then run forever in the background, executing the target script every 15 minutes.
"""

import subprocess
import time
import sys
import os
from datetime import datetime

def log_message(message):
    """Print message with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_target_script():
    """Run the target Python script"""
    try:
        log_message("🚀 Starting execution of run_local.py...")

        # Check if this is a bot startup script (contains infinite loop)
        try:
            with open("run_local.py", "r", encoding="utf-8") as f:
                content = f.read()
                has_infinite_loop = "while True:" in content or "bot.polling()" in content
        except UnicodeDecodeError:
            # If UTF-8 fails, try with default encoding
            with open("run_local.py", "r", encoding="latin-1") as f:
                content = f.read()
                has_infinite_loop = "while True:" in content or "bot.polling()" in content

        if has_infinite_loop:
            log_message("🤖 Detected bot startup script with infinite loop")
            log_message("🚀 Starting bots in background...")

            # Start the script in background and let it run
            process = subprocess.Popen(
                [sys.executable, "run_local.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )

            # Give it a moment to start
            time.sleep(5)

            # Check if it's still running
            if process.poll() is None:
                log_message("✅ Bots started successfully and running in background")
                log_message(f"📊 Process ID: {process.pid}")
            else:
                log_message("❌ Bot startup script exited unexpectedly")
                stdout, stderr = process.communicate()
                if stderr:
                    print("🚨 Error output:")
                    print(stderr.decode())
        else:
            # Regular script - run and wait for completion
            result = subprocess.run(
                [sys.executable, "run_local.py"],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
                timeout=60
            )

            if result.returncode == 0:
                log_message("✅ run_local.py completed successfully")
                if result.stdout:
                    print("📄 Output:")
                    print(result.stdout)
            else:
                log_message(f"❌ run_local.py failed with return code: {result.returncode}")
                if result.stderr:
                    print("🚨 Error output:")
                    print(result.stderr)

    except subprocess.TimeoutExpired:
        log_message("⏰ run_local.py timed out after 60 seconds")
    except FileNotFoundError:
        log_message("❌ Error: run_local.py not found in current directory")
    except Exception as e:
        log_message(f"❌ Unexpected error: {e}")

def main():
    """Main infinite loop function"""
    log_message("🔄 Infinite Loop Script Started")
    log_message("📋 Will run 'python .\\run_local.py' every 15 minutes")
    log_message("🛑 Press Ctrl+C to stop the loop")
    log_message("=" * 60)
    
    # Check if target script exists
    if not os.path.exists("run_local.py"):
        log_message("⚠️  Warning: run_local.py not found in current directory")
        log_message("📁 Current directory: " + os.getcwd())
        log_message("📋 Make sure run_local.py exists before continuing")
        
        response = input("\nDo you want to continue anyway? (y/n): ")
        if response.lower() != 'y':
            log_message("🛑 Script stopped by user")
            return
    
    execution_count = 0
    
    try:
        while True:
            execution_count += 1
            log_message(f"🔢 Execution #{execution_count}")
            
            # Run the target script
            run_target_script()
            
            # Calculate next execution time
            from datetime import timedelta
            next_run = datetime.now() + timedelta(minutes=15)
            next_run = next_run.replace(second=0, microsecond=0)
            
            log_message(f"⏰ Next execution scheduled at: {next_run.strftime('%H:%M:%S')}")
            log_message("💤 Waiting 15 minutes...")
            log_message("-" * 60)
            
            # Wait for 15 minutes (900 seconds)
            time.sleep(900)
            
    except KeyboardInterrupt:
        log_message("\n🛑 Infinite loop stopped by user (Ctrl+C)")
        log_message(f"📊 Total executions completed: {execution_count}")
        log_message("👋 Goodbye!")
    except Exception as e:
        log_message(f"💥 Fatal error in main loop: {e}")
        log_message("🔄 Script will exit")

if __name__ == "__main__":
    main()
