#!/usr/bin/env python3
"""
Infinite Loop Script - Auto-Execution Every 15 Minutes

This script runs 'python .\\start_all_python.py' every 15 minutes automatically.
Just run this script once with: python infinite_loop.py
It will then run forever in the background, executing the target script every 15 minutes.
"""

import subprocess
import time
import sys
import os
from datetime import datetime

def log_message(message):
    """Print message with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_target_script():
    """Run the target Python script"""
    try:
        log_message("🚀 Starting execution of start_all_python.py...")
        
        # Run the target script
        result = subprocess.run(
            [sys.executable, ".\\start_all_python.py"],
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            log_message("✅ start_all_python.py completed successfully")
            if result.stdout:
                print("📄 Output:")
                print(result.stdout)
        else:
            log_message(f"❌ start_all_python.py failed with return code: {result.returncode}")
            if result.stderr:
                print("🚨 Error output:")
                print(result.stderr)
                
    except FileNotFoundError:
        log_message("❌ Error: start_all_python.py not found in current directory")
    except Exception as e:
        log_message(f"❌ Unexpected error: {e}")

def main():
    """Main infinite loop function"""
    log_message("🔄 Infinite Loop Script Started")
    log_message("📋 Will run 'python .\\start_all_python.py' every 15 minutes")
    log_message("🛑 Press Ctrl+C to stop the loop")
    log_message("=" * 60)
    
    # Check if target script exists
    if not os.path.exists("start_all_python.py"):
        log_message("⚠️  Warning: start_all_python.py not found in current directory")
        log_message("📁 Current directory: " + os.getcwd())
        log_message("📋 Make sure start_all_python.py exists before continuing")
        
        response = input("\nDo you want to continue anyway? (y/n): ")
        if response.lower() != 'y':
            log_message("🛑 Script stopped by user")
            return
    
    execution_count = 0
    
    try:
        while True:
            execution_count += 1
            log_message(f"🔢 Execution #{execution_count}")
            
            # Run the target script
            run_target_script()
            
            # Calculate next execution time
            next_run = datetime.now().replace(second=0, microsecond=0)
            next_run = next_run.replace(minute=next_run.minute + 15)
            
            log_message(f"⏰ Next execution scheduled at: {next_run.strftime('%H:%M:%S')}")
            log_message("💤 Waiting 15 minutes...")
            log_message("-" * 60)
            
            # Wait for 15 minutes (900 seconds)
            time.sleep(900)
            
    except KeyboardInterrupt:
        log_message("\n🛑 Infinite loop stopped by user (Ctrl+C)")
        log_message(f"📊 Total executions completed: {execution_count}")
        log_message("👋 Goodbye!")
    except Exception as e:
        log_message(f"💥 Fatal error in main loop: {e}")
        log_message("🔄 Script will exit")

if __name__ == "__main__":
    main()
