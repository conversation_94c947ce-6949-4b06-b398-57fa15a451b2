#!/usr/bin/env python3
"""
Test bot initialization to identify the proxy error
"""

import os

def test_teacher_bot():
    """Test Teacher Bot initialization"""
    print("🧪 Testing Teacher Bot initialization...")
    try:
        # Set environment variable
        os.environ['ENVIRONMENT'] = 'local'

        # Try to import telebot first
        import telebot
        print("✅ telebot module imported")

        # Try to create a simple bot instance
        TOKEN = "7432401952:AAHm4Sez4z8_zzwmo3A_k2eY-gcYDn8j0Z8"
        test_bot = telebot.TeleBot(TOKEN)
        print("✅ Simple bot instance created")

        # Test basic functionality
        me = test_bot.get_me()
        print(f"✅ Teacher Bot info: @{me.username}")

        # Now try importing the full module
        from TelegramBot import bot
        print("✅ Teacher Bot module imported successfully!")

    except Exception as e:
        print(f"❌ Teacher Bot error: {e}")
        import traceback
        traceback.print_exc()

def test_student_bot():
    """Test Student Bot initialization"""
    print("\n🧪 Testing Student Bot initialization...")
    try:
        from StudentBot import student_bot
        print("✅ Student Bot imported successfully!")
        
        # Test basic functionality
        me = student_bot.get_me()
        print(f"✅ Student Bot info: @{me.username}")
        
    except Exception as e:
        print(f"❌ Student Bot error: {e}")
        import traceback
        traceback.print_exc()

def test_webserver():
    """Test webserver"""
    print("\n🧪 Testing webserver...")
    try:
        import webserver
        print("✅ Webserver module imported successfully!")
        
        # Test starting webserver
        webserver.keep_alive()
        print("✅ Webserver started successfully!")
        
    except Exception as e:
        print(f"❌ Webserver error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 Diagnosing bot initialization issues...\n")
    
    test_teacher_bot()
    test_student_bot()
    test_webserver()
    
    print("\n✅ Diagnosis complete!")
