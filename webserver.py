from flask import Flask
from threading import Thread
import socket

app = Flask(__name__)

@app.route('/')
def home():
    return "Telegram Bot is running!"

def find_free_port():
    """Find a free port to use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port

def run():
    port = find_free_port()
    print(f"🌐 Webserver starting on http://0.0.0.0:{port}")
    app.run(host="0.0.0.0", port=port, debug=False)

def keep_alive():
    """Start the Flask webserver in a separate thread"""
    t = Thread(target=run, daemon=True)
    t.start()